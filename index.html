<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta
            name="viewport"
            content="width=device-width, initial-scale=1.0"
        />
        <title>SVG Animation Studio</title>
        <link
            rel="stylesheet"
            href="styles.css"
        />
    </head>
    <body>
        <div class="app-container">
            <header class="app-header">
                <h1>SVG Animation Studio</h1>
            </header>

            <main class="app-main">
                <!-- Canvas Section (Left) -->
                <section class="canvas-section">
                    <div class="canvas-header">
                        <h2>Canvas</h2>
                        <div class="canvas-controls">
                            <button
                                id="startAnimation"
                                class="btn btn-success"
                            >
                                <span class="btn-icon">▶</span> Play
                            </button>
                            <button
                                id="stopAnimation"
                                class="btn btn-danger"
                            >
                                <span class="btn-icon">⏸</span> Stop
                            </button>
                        </div>
                    </div>
                    <div
                        id="svgPreview"
                        class="svg-canvas"
                    ></div>
                </section>

                <!-- Inspector Section (Right) -->
                <section class="inspector-section">
                    <div class="inspector-header">
                        <h2>Inspector</h2>
                    </div>

                    <div class="inspector-content">
                        <!-- Animation Selection -->
                        <div class="inspector-panel">
                            <h3>Animation</h3>
                            <div class="form-group">
                                <label for="animationFolder"
                                    >Animation Type:</label
                                >
                                <select id="animationFolder">
                                    <option value="">
                                        Select animation...
                                    </option>
                                    <!-- Animation folders will be populated by JavaScript -->
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="svgFileSelector">Frame:</label>
                                <select id="svgFileSelector">
                                    <option value="">Select a frame...</option>
                                    <!-- Files will be populated by JavaScript -->
                                </select>
                            </div>
                        </div>

                        <!-- Animation Settings -->
                        <div class="inspector-panel">
                            <h3>Animation Settings</h3>
                            <div class="form-group">
                                <label for="animationSpeed">Speed (ms):</label>
                                <input
                                    type="number"
                                    id="animationSpeed"
                                    value="30"
                                    min="10"
                                    max="1000"
                                    class="form-control"
                                />
                            </div>
                        </div>

                        <!-- Sprite Modification -->
                        <div class="inspector-panel">
                            <h3>Sprite Modification</h3>
                            <div class="form-group">
                                <label for="spriteReplacementFile"
                                    >Replacement File:</label
                                >
                                <select
                                    id="spriteReplacementFile"
                                    class="form-control"
                                >
                                    <option value="">
                                        Select a replacement file...
                                    </option>
                                    <!-- Files will be populated by JavaScript -->
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="spriteId">Sprite ID:</label>
                                <div class="sprite-id-container">
                                    <input
                                        type="text"
                                        id="spriteId"
                                        placeholder="Select from list or enter manually"
                                        class="form-control"
                                    />
                                    <button
                                        id="analyzeSprites"
                                        class="btn btn-secondary btn-sm"
                                    >
                                        Show All
                                    </button>
                                </div>
                            </div>
                            <div
                                id="spritesList"
                                class="sprites-list"
                            ></div>
                            <div class="form-actions">
                                <button
                                    id="replaceSprite"
                                    class="btn btn-primary"
                                >
                                    Replace Sprite in All Files
                                </button>
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="inspector-panel">
                            <h3>Actions</h3>
                            <div class="form-actions">
                                <button
                                    id="restoreAllSvg"
                                    class="btn btn-danger"
                                >
                                    Restore All SVG Files
                                </button>
                            </div>
                        </div>
                    </div>
                </section>
            </main>

            <footer class="status-bar">
                <span id="statusMessage">Ready</span>
            </footer>
        </div>

        <script src="script.js"></script>
    </body>
</html>
