* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
        Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    height: 100vh;
    overflow: hidden;
}

.app-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: #ffffff;
    margin: 0;
}

.app-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 10;
}

.app-header h1 {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    text-align: center;
}

.app-main {
    flex: 1;
    display: flex;
    overflow: hidden;
}

/* Canvas Section (Left) */
.canvas-section {
    flex: 2;
    display: flex;
    flex-direction: column;
    background: #f8fafc;
    border-right: 1px solid #e2e8f0;
}

.canvas-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: white;
    border-bottom: 1px solid #e2e8f0;
}

.canvas-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1a202c;
    margin: 0;
}

.canvas-controls {
    display: flex;
    gap: 0.5rem;
}

.svg-canvas {
    flex: 1;
    background: white;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: auto;
    margin: 1rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 2px dashed #e2e8f0;
    transition: border-color 0.3s ease;
}

.svg-canvas:hover {
    border-color: #cbd5e0;
}

.svg-canvas svg {
    max-width: 100%;
    max-height: 100%;
    filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
}

/* Inspector Section (Right) */
.inspector-section {
    flex: 1;
    min-width: 350px;
    background: white;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.inspector-header {
    padding: 1rem 1.5rem;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
}

.inspector-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1a202c;
    margin: 0;
}

.inspector-content {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
}

.inspector-panel {
    background: #f8fafc;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid #e2e8f0;
}

.inspector-panel h3 {
    font-size: 1rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e2e8f0;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group:last-child {
    margin-bottom: 0;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #4a5568;
    font-size: 0.875rem;
}

.form-control {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.875rem;
    background-color: white;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.sprite-id-container {
    display: flex;
    gap: 0.5rem;
    align-items: flex-end;
}

.sprite-id-container .form-control {
    flex: 1;
}

.form-actions {
    margin-top: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

/* Modern Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border: 1px solid transparent;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.15s ease-in-out;
    gap: 0.5rem;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.sprites-list {
    max-height: 300px;
    overflow-y: auto;
    margin-top: 10px;
    margin-bottom: 15px;
    border: 2px solid #3498db;
    border-radius: 4px;
    display: none;
    position: relative;
    z-index: 100;
    background-color: white;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.sprites-list.active {
    display: block;
}

.sprite-item {
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sprite-item:hover {
    background-color: #f5f5f5;
}

.sprite-item:last-child {
    border-bottom: none;
}

.sprite-item.selected {
    background-color: #667eea;
    color: white;
    border-left: 4px solid #5a67d8;
}

.sprite-item.selected strong {
    color: white;
}

.sprite-item.selected .sprite-id {
    color: #e2e8f0;
}

.sprite-item strong {
    color: #3498db;
    font-size: 14px;
}

.sprite-id {
    color: #7f8c8d;
    font-size: 12px;
    margin-left: 5px;
}

/* Highlight sprites with specific names */
.sprite-item strong[data-name="a_Mouth"],
.sprite-item strong[data-name="a_MouthEmote"] {
    color: #e74c3c;
    font-weight: bold;
}

.sprite-id {
    font-weight: bold;
    color: #34495e;
}

.mouth-sprite {
    background-color: rgba(231, 76, 60, 0.1);
    border-left: 3px solid #e74c3c;
}

.mouth-sprite:hover {
    background-color: rgba(231, 76, 60, 0.2);
}

.eye-sprite {
    background-color: rgba(52, 152, 219, 0.1);
    border-left: 3px solid #3498db;
}

.eye-sprite:hover {
    background-color: rgba(52, 152, 219, 0.2);
}

.hair-sprite {
    background-color: rgba(155, 89, 182, 0.1);
    border-left: 3px solid #9b59b6;
}

.hair-sprite:hover {
    background-color: rgba(155, 89, 182, 0.2);
}

.head-sprite {
    background-color: rgba(46, 204, 113, 0.1);
    border-left: 3px solid #2ecc71;
}

.head-sprite:hover {
    background-color: rgba(46, 204, 113, 0.2);
}

.sprite-section-header {
    background-color: #34495e;
    color: white;
    padding: 5px 10px;
    font-weight: bold;
    text-align: center;
    border-bottom: 1px solid #2c3e50;
    position: sticky;
    top: 0;
    z-index: 10;
}

.close-button {
    text-align: center;
    padding: 8px;
    background-color: #e74c3c;
    color: white;
    cursor: pointer;
    font-weight: bold;
}

.close-button:hover {
    background-color: #c0392b;
}

.form-group {
    margin-bottom: 10px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.form-group input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.input-with-button {
    display: flex;
    gap: 10px;
}

.input-with-button input {
    flex: 1;
}

.small-button {
    padding: 5px 10px;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.small-button:hover {
    background-color: #2980b9;
}

.elements-list {
    margin-top: 10px;
    max-height: 150px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    background-color: white;
    display: none;
}

.elements-list.active {
    display: block;
}

.element-item {
    padding: 5px;
    cursor: pointer;
    border-bottom: 1px solid #eee;
}

.element-item:hover {
    background-color: #f0f0f0;
}

.element-item:last-child {
    border-bottom: none;
}

.close-button {
    text-align: center;
    padding: 5px;
    background-color: #e74c3c;
    color: white;
    cursor: pointer;
    border-radius: 4px;
    margin-top: 5px;
}

.close-button:hover {
    background-color: #c0392b;
}

.action-button {
    padding: 10px 15px;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

.action-button:hover {
    background-color: #2980b9;
}

.action-button.danger {
    background-color: #e74c3c;
}

.action-button.danger:hover {
    background-color: #c0392b;
}

#restoreAllSvg {
    margin-top: 20px;
    font-weight: bold;
    background-color: #e74c3c;
    border: 2px solid #c0392b;
}

.action-button.success {
    background-color: #27ae60;
}

.action-button.success:hover {
    background-color: #219955;
}

.action-button.warning {
    background-color: #f39c12;
}

.action-button.warning:hover {
    background-color: #e67e22;
}

.preview-container {
    margin-top: 20px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 5px;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.05);
}

.svg-preview {
    width: 100%;
    height: 400px;
    border: 1px solid #ddd;
    background-color: white;
    overflow: auto;
    display: flex;
    justify-content: center;
    align-items: center;
}

.svg-preview svg {
    max-width: 100%;
    max-height: 100%;
}

.status-bar {
    margin-top: 20px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 5px;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.05);
}

#statusMessage {
    font-weight: bold;
}

.success {
    color: #27ae60;
}

.error {
    color: #e74c3c;
}

.warning {
    color: #f39c12;
}

/* Additional Modern Button Styles */
.btn-primary {
    background-color: #667eea;
    border-color: #667eea;
    color: white;
}

.btn-primary:hover {
    background-color: #5a67d8;
    border-color: #5a67d8;
}

.btn-secondary {
    background-color: #718096;
    border-color: #718096;
    color: white;
}

.btn-secondary:hover {
    background-color: #4a5568;
    border-color: #4a5568;
}

.btn-success {
    background-color: #48bb78;
    border-color: #48bb78;
    color: white;
}

.btn-success:hover {
    background-color: #38a169;
    border-color: #38a169;
}

.btn-danger {
    background-color: #f56565;
    border-color: #f56565;
    color: white;
}

.btn-danger:hover {
    background-color: #e53e3e;
    border-color: #e53e3e;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

.btn-icon {
    font-size: 0.75rem;
}

/* Toggle Button States */
.btn[data-state="hidden"] {
    background-color: #718096;
    border-color: #718096;
    color: white;
}

.btn[data-state="hidden"]:hover {
    background-color: #4a5568;
    border-color: #4a5568;
}

.btn[data-state="visible"] {
    background-color: #667eea;
    border-color: #667eea;
    color: white;
}

.btn[data-state="visible"]:hover {
    background-color: #5a67d8;
    border-color: #5a67d8;
}

/* Color Picker Section */
.color-picker-section {
    margin-top: 1rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.color-picker-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e2e8f0;
}

.color-picker-header h4 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: #2d3748;
}

.sprite-type-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.sprite-type-badge.mouth {
    background-color: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
    border: 1px solid #e74c3c;
}

.sprite-type-badge.eye {
    background-color: rgba(52, 152, 219, 0.1);
    color: #3498db;
    border: 1px solid #3498db;
}

.sprite-type-badge.hair {
    background-color: rgba(155, 89, 182, 0.1);
    color: #9b59b6;
    border: 1px solid #9b59b6;
}

.sprite-type-badge.head {
    background-color: rgba(46, 204, 113, 0.1);
    color: #2ecc71;
    border: 1px solid #2ecc71;
}

.color-swatches {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.75rem;
}

.color-swatch {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    background: white;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
    transition: all 0.15s ease-in-out;
}

.color-swatch:hover {
    border-color: #cbd5e0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.color-preview {
    width: 2rem;
    height: 2rem;
    border-radius: 4px;
    border: 2px solid #e2e8f0;
    cursor: pointer;
    transition: border-color 0.15s ease-in-out;
}

.color-preview:hover {
    border-color: #667eea;
}

.color-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.color-value {
    font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
    font-size: 0.75rem;
    color: #4a5568;
    background: #f7fafc;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    border: 1px solid #e2e8f0;
}

.color-picker-input {
    width: 2rem;
    height: 2rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    opacity: 0;
    position: absolute;
}

.color-picker-wrapper {
    position: relative;
    display: inline-block;
}
